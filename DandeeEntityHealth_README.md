# DandeeEntityHealth

A Rust Oxide plugin that displays health bars for entities when they take damage, providing visual feedback to players about entity health status.

## Features

- **Visual Health Bars**: Displays health bars above entities when they take damage
- **Entity Names**: Shows entity names above health bars (configurable)
- **Color-Coded Health**: Health bars change color based on health percentage (green → yellow → red)
- **Customizable Display**: Fully configurable health bar appearance, duration, and positioning
- **Permission-Based**: Only players with permission can see health bars
- **Auto-Cleanup**: Health bars automatically disappear after a set duration or when entities die

## Installation

1. **Download** the `DandeeEntityHealth.cs` file
2. **Place** the file in your `oxide/plugins/` directory
3. **Restart** your server or use `oxide.reload DandeeEntityHealth` in console
4. **Configure** the plugin by editing the generated config file (optional)

## Permissions

The plugin uses a permission system to control who can see health bars. **Players must have permission to see health bars.**

### Default Permission
- **Permission Name**: `dandeeentityhealth.use`

### Granting Permissions

#### Individual Players
```
oxide.grant user <username> dandeeentityhealth.use
oxide.grant user <steamid> dandeeentityhealth.use
```

#### Groups
```
oxide.grant group <groupname> dandeeentityhealth.use
```

#### Common Examples
```
// Grant to specific player
oxide.grant user "PlayerName" dandeeentityhealth.use
oxide.grant user 76561198000000000 dandeeentityhealth.use

// Grant to admin group
oxide.grant group admin dandeeentityhealth.use

// Grant to VIP group
oxide.grant group vip dandeeentityhealth.use

// Grant to all players (default group)
oxide.grant group default dandeeentityhealth.use
```

### Revoking Permissions
```
oxide.revoke user <username> dandeeentityhealth.use
oxide.revoke group <groupname> dandeeentityhealth.use
```

### Checking Permissions
```
oxide.show user <username>
oxide.show group <groupname>
```

## Configuration

The plugin generates a configuration file at `oxide/config/DandeeEntityHealth.json` with the following options:

```json
{
  "Permission Required": "dandeeentityhealth.use",
  "Show Health Bar": true,
  "Show Entity Names": true,
  "Health Bar Duration (seconds)": 5.0,
  "Health Bar Width": 100.0,
  "Health Bar Height": 10.0,
  "Health Bar Position": 45.0,
  "Health Bar Colors": {
    "Background Color": "0.1 0.1 0.1 0.7",
    "Health Color High": "0.1 0.8 0.1 1",
    "Health Color Medium": "0.9 0.9 0.1 1",
    "Health Color Low": "0.9 0.1 0.1 1",
    "Border Color": "0.1 0.1 0.1 1",
    "Text Color": "1 1 1 1"
  }
}
```

### Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `Permission Required` | Permission needed to see health bars | `dandeeentityhealth.use` |
| `Show Health Bar` | Enable/disable health bar display | `true` |
| `Show Entity Names` | Show entity names above health bars | `true` |
| `Health Bar Duration` | How long health bars stay visible (seconds) | `5.0` |
| `Health Bar Width` | Width of health bars in pixels | `100.0` |
| `Health Bar Height` | Height of health bars in pixels | `10.0` |
| `Health Bar Position` | Vertical position offset from center | `45.0` |

### Color Configuration

Colors use RGBA format (Red Green Blue Alpha) with values from 0 to 1:

- **Background Color**: Health bar background
- **Health Color High**: Color when health > 66% (default: green)
- **Health Color Medium**: Color when health 33-66% (default: yellow)
- **Health Color Low**: Color when health < 33% (default: red)
- **Border Color**: Health bar border color
- **Text Color**: Health text color

## Usage

1. **Grant Permission**: Ensure players have the `dandeeentityhealth.use` permission
2. **Deal Damage**: When a player with permission damages an entity, a health bar appears
3. **View Health**: The health bar shows current/max health and changes color based on health percentage
4. **Auto-Hide**: Health bars automatically disappear after the configured duration

## Supported Entities

The plugin works with all `BaseCombatEntity` types, including:

- **Players** - Shows player display names
- **NPCs/Animals** - Shows cleaned-up entity names (e.g., "Bear", "Wolf")
- **Structures** - Shows building component names
- **Vehicles** - Shows vehicle type names
- **Other Entities** - Shows generic entity names

**Note**: Corpses are excluded from health bar display.

## Commands

This plugin does not provide chat commands. All functionality is automatic based on damage events.

## API

This plugin does not currently expose any API hooks for other plugins.

## Troubleshooting

### Health bars not showing
- **Check Permission**: Verify the player has the `dandeeentityhealth.use` permission
- **Check Config**: Ensure `Show Health Bar` is set to `true` in config
- **Entity Type**: Ensure the entity being damaged is not a corpse
- **Reload Plugin**: Try `oxide.reload DandeeEntityHealth` in console

### Configuration not loading
- Check the JSON syntax in your config file
- Delete the config file to regenerate defaults
- Check server console for configuration errors

### Performance issues
- Reduce `Health Bar Duration` to decrease UI load
- Consider limiting permissions to reduce active health bars

## Version History

- **v1.0.0** - Initial release with core health bar functionality

## Support

For support, bug reports, or feature requests, please contact Dandee Studios.

## License

This plugin is developed by Dandee Studios. Please respect the author's work and licensing terms.
