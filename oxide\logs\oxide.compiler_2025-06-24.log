2025-06-24 19:37:39.899 -05:00 [INF] Starting compiler v1.0.32.0. . .
2025-06-24 19:37:39.911 -05:00 [INF] Minimal logging level is set to Debug
2025-06-24 19:37:39.912 -05:00 [INF] Watching parent process ([24912] RustDedicated) for shutdown
2025-06-24 19:37:39.913 -05:00 [DBG] Started message server. . .
2025-06-24 19:37:39.913 -05:00 [INF] Message server has started
2025-06-24 19:37:41.914 -05:00 [INF] Sent ready message to parent process
2025-06-24 19:37:41.915 -05:00 [DBG] Compiler has started successfully and is awaiting jobs. . .
2025-06-24 19:37:41.998 -05:00 [DBG] Received compile job 0 | Plugins: 10, References: 57
2025-06-24 19:37:42.917 -05:00 [INF] Starting compilation of job 0 | Total Plugins: 10
2025-06-24 19:37:42.918 -05:00 [DBG] Settings[Encoding: utf-8, CSVersion: Preview, Target: DynamicallyLinkedLibrary, Platform: AnyCpu, StdLib: False, Debug: False, Preprocessor: OXIDE, OXIDEMOD, RUST, RUST_MASTER, RUST_2_0_6485, RUST_2_0_6485_MASTER, OXIDE_PUBLICIZED]
Reference Files:
  - [1] mscorlib.dll(4631552)
  - [2] Oxide.Core.dll(201728)
  - [3] Oxide.CSharp.dll(104960)
  - [4] Oxide.Common.dll(22528)
  - [5] System.dll(2639360)
  - [6] System.Core.dll(1113088)
  - [7] System.Data.dll(2081280)
  - [8] System.Xml.dll(3160064)
  - [9] Oxide.References.dll(1419264)
  - [10] Oxide.MySql.dll(20480)
  - [11] MySql.Data.dll(443392)
  - [12] Oxide.SQLite.dll(20992)
  - [13] System.Data.SQLite.dll(286720)
  - [14] Oxide.Unity.dll(10240)
  - [15] UnityEngine.dll(125120)
  - [16] Oxide.Rust.dll(98816)
  - [17] Assembly-CSharp.dll(7131136)
  - [18] Facepunch.System.dll(74752)
  - [19] Facepunch.Console.dll(24576)
  - [20] UnityEngine.CoreModule.dll(1387200)
  - [21] Facepunch.Network.dll(72704)
  - [22] Rust.Platform.Common.dll(23552)
  - [23] UnityEngine.TextRenderingModule.dll(37568)
  - [24] UnityEngine.UI.dll(240640)
  - [25] Rust.Data.dll(1392640)
  - [26] Rust.Global.dll(99840)
  - [27] Facepunch.Steamworks.Win64.dll(637440)
  - [28] Facepunch.UnityEngine.dll(120832)
  - [29] Assembly-CSharp-firstpass.dll(1707520)
  - [30] Facepunch.Unity.dll(76800)
  - [31] 0Harmony.dll(2193920)
  - [32] Rust.FileSystem.dll(18944)
  - [33] Rust.Clans.dll(12288)
  - [34] Rust.Clans.Local.dll(59392)
  - [35] Rust.Localization.dll(5632)
  - [36] Rust.Platform.dll(7168)
  - [37] Rust.Platform.Steam.dll(30720)
  - [38] Rust.Workshop.dll(557056)
  - [39] Rust.World.dll(12800)
  - [40] System.Drawing.dll(481280)
  - [41] UnityEngine.AIModule.dll(57024)
  - [42] UnityEngine.AssetBundleModule.dll(31424)
  - [43] UnityEngine.GridModule.dll(23744)
  - [44] UnityEngine.ImageConversionModule.dll(22720)
  - [45] UnityEngine.PhysicsModule.dll(136896)
  - [46] UnityEngine.TerrainModule.dll(99520)
  - [47] UnityEngine.TerrainPhysicsModule.dll(19648)
  - [48] UnityEngine.UIModule.dll(35008)
  - [49] UnityEngine.UIElementsModule.dll(1533120)
  - [50] UnityEngine.UnityWebRequestAudioModule.dll(21184)
  - [51] UnityEngine.UnityWebRequestModule.dll(56512)
  - [52] UnityEngine.UnityWebRequestTextureModule.dll(20160)
  - [53] UnityEngine.UnityWebRequestWWWModule.dll(29888)
  - [54] UnityEngine.VehiclesModule.dll(22720)
  - [55] netstandard.dll(90112)
  - [56] Facepunch.Sqlite.dll(19456)
  - [57] UnityEngine.SharedInternalsModule.dll(31424)
Plugin Files:
  - [1] CopyPaste.cs(206767)
  - [2] DandeeEntityHealth.cs(14613)
  - [3] DiscordMessages.cs(42797)
  - [4] ImageLibrary.cs(54464)
  - [5] Notify.cs(38221)
  - [6] PerformanceMonitor.cs(12263)
  - [7] PermissionsManager.cs(76658)
  - [8] RemoverTool.cs(215901)
  - [9] TruePVE.cs(211597)
  - [10] ZoneManager.cs(159344)
2025-06-24 19:37:42.926 -05:00 [DBG] Added 57 project references
2025-06-24 19:37:43.034 -05:00 [DBG] Added 10 plugins to the project
2025-06-24 19:37:43.736 -05:00 [INF] Successfully compiled 10/10 plugins for job 0 in 819ms
2025-06-24 19:37:43.736 -05:00 [DBG] Pushing job 0 back to parent
2025-06-24 19:38:37.002 -05:00 [INF] Termination request has been received from compiler stream
2025-06-24 19:38:37.762 -05:00 [INF] Termination request has been received from SIGTERM
