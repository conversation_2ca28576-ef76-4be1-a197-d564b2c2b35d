using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Dandee Entity Health", "Dandee Studios", "1.0.0")]
    [Description("Displays health bars for entities when damaged")]
    class DandeeEntityHealth : RustPlugin
    {
        #region Fields

        private const string UIHealthBar = "DandeeEntityHealth_HealthBar";

        private Dictionary<ulong, Dictionary<NetworkableId, float>> entityHealthData = new Dictionary<ulong, Dictionary<NetworkableId, float>>();

        #endregion

        #region Configuration

        private Configuration config;

        private class Configuration
        {
            [JsonProperty("Permission Required")]
            public string Permission = "dandeeentityhealth.use";

            [JsonProperty("Show Health Bar")]
            public bool ShowHealthBar = true;
            
            [JsonProperty("Show Entity Names")]
            public bool ShowEntityNames = true;

            [JsonProperty("Health Bar Duration (seconds)")]
            public float HealthBarDuration = 5.0f;

            [JsonProperty("Health Bar Width")]
            public float HealthBarWidth = 100f;

            [JsonProperty("Health Bar Height")]
            public float HealthBarHeight = 10f;
            
            [JsonProperty("Health Bar Position")]
            public float HealthBarPosition = 45f;

            [JsonProperty("Health Bar Colors")]
            public HealthBarColors BarColors = new HealthBarColors();

            public class HealthBarColors
            {
                [JsonProperty("Background Color")]
                public string BackgroundColor = "0.1 0.1 0.1 0.7";
                
                [JsonProperty("Health Color High")]
                public string HealthColorHigh = "0.1 0.8 0.1 1";
                
                [JsonProperty("Health Color Medium")]
                public string HealthColorMedium = "0.9 0.9 0.1 1";
                
                [JsonProperty("Health Color Low")]
                public string HealthColorLow = "0.9 0.1 0.1 1";
                
                [JsonProperty("Border Color")]
                public string BorderColor = "0.1 0.1 0.1 1";
                
                [JsonProperty("Text Color")]
                public string TextColor = "1 1 1 1";
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) throw new Exception();
                SaveConfig();
            }
            catch
            {
                PrintError("Your configuration file contains an error. Using default configuration values.");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = new Configuration();

        protected override void SaveConfig() => Config.WriteObject(config);

        #endregion

        #region Hooks

        private void Init()
        {
            permission.RegisterPermission(config.Permission, this);
        }

        private void Unload()
        {
            // Clean up health bars
            foreach (var player in BasePlayer.activePlayerList)
            {
                // Clean up any health bars
                if (entityHealthData.ContainsKey(player.userID))
                {
                    foreach (var entityId in entityHealthData[player.userID].Keys)
                    {
                        string entityIdString = entityId.ToString();
                        CuiHelper.DestroyUi(player, UIHealthBar + "_" + entityIdString);
                        CuiHelper.DestroyUi(player, UIHealthBar + "_Name_" + entityIdString);
                    }
                }
            }
            
            entityHealthData.Clear();
        }

        private void OnEntityTakeDamage(BaseCombatEntity entity, HitInfo info)
        {
            if (entity == null || info == null || info.Initiator == null) return;
            
            var attacker = info.Initiator.ToPlayer();
            if (attacker == null) return;
            
            if (!permission.UserHasPermission(attacker.UserIDString, config.Permission)) return;

            // Don't show health bars for corpses
            if (entity is BaseCorpse) return;
            
            // Store entity's max health if we don't have it yet
            if (!entityHealthData.ContainsKey(attacker.userID))
                entityHealthData[attacker.userID] = new Dictionary<NetworkableId, float>();
                
            // Store or update the entity's max health
            float maxHealth = entity.MaxHealth();
            if (!entityHealthData[attacker.userID].ContainsKey(entity.net.ID) || 
                entityHealthData[attacker.userID][entity.net.ID] < entity.health)
            {
                entityHealthData[attacker.userID][entity.net.ID] = maxHealth;
            }
            
            // Show health bar for all entities if enabled
            if (config.ShowHealthBar && entity.health > 0)
            {
                ShowHealthBar(attacker, entity, maxHealth);
            }
        }
        
        private void OnPlayerDisconnected(BasePlayer player)
        {
            if (player == null) return;
            
            // Clean up health bars data for this player
            if (entityHealthData.ContainsKey(player.userID))
            {
                entityHealthData.Remove(player.userID);
            }
        }
        
        private void OnEntityDeath(BaseCombatEntity entity, HitInfo info)
        {
            if (entity == null) return;
            
            string entityIdString = entity.net.ID.ToString();
            
            // Clean up health bars for this entity for all players
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, UIHealthBar + "_" + entityIdString);
                CuiHelper.DestroyUi(player, UIHealthBar + "_Name_" + entityIdString);
                
                // Remove entity from health data
                if (entityHealthData.ContainsKey(player.userID) && 
                    entityHealthData[player.userID].ContainsKey(entity.net.ID))
                {
                    entityHealthData[player.userID].Remove(entity.net.ID);
                }
            }
        }

        #endregion

        #region Core Functions

        private void ShowHealthBar(BasePlayer player, BaseCombatEntity entity, float maxHealth)
        {
            if (player == null || entity == null) return;
            
            // Get the entity's current health percentage
            float healthPercentage = Mathf.Clamp01(entity.health / maxHealth);
            
            // Create health bar UI
            var container = new CuiElementContainer();
            
            // Health bar background
            string entityIdString = entity.net.ID.ToString();
            container.Add(new CuiElement
            {
                Parent = "Hud",
                Name = UIHealthBar + "_" + entityIdString,
                Components =
                {
                    new CuiImageComponent
                    {
                        Color = config.BarColors.BackgroundColor
                    },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.5 0.5",
                        AnchorMax = "0.5 0.5",
                        OffsetMin = $"{-config.HealthBarWidth/2} {config.HealthBarPosition}",
                        OffsetMax = $"{config.HealthBarWidth/2} {config.HealthBarPosition + config.HealthBarHeight}"
                    }
                }
            });
            
            // Health bar fill
            string healthColor = GetHealthColor(healthPercentage);
            container.Add(new CuiElement
            {
                Parent = UIHealthBar + "_" + entityIdString,
                Name = UIHealthBar + "_Fill_" + entityIdString,
                Components =
                {
                    new CuiImageComponent
                    {
                        Color = healthColor
                    },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = $"{healthPercentage} 1"
                    }
                }
            });
            
            // Entity name display (above health bar)
            if (config.ShowEntityNames)
            {
                string entityName = GetEntityName(entity);
                if (!string.IsNullOrEmpty(entityName))
                {
                    container.Add(new CuiElement
                    {
                        Parent = "Hud",
                        Name = UIHealthBar + "_Name_" + entityIdString,
                        Components =
                        {
                            new CuiTextComponent
                            {
                                Text = entityName,
                                FontSize = 12,
                                Align = TextAnchor.LowerCenter,
                                Color = config.BarColors.TextColor,
                                FadeIn = 0.1f
                            },
                            new CuiRectTransformComponent
                            {
                                AnchorMin = "0.5 0.5",
                                AnchorMax = "0.5 0.5",
                                OffsetMin = $"{-config.HealthBarWidth/2} {config.HealthBarPosition + config.HealthBarHeight}",
                                OffsetMax = $"{config.HealthBarWidth/2} {config.HealthBarPosition + config.HealthBarHeight + 20}"
                            },
                            new CuiOutlineComponent
                            {
                                Color = "0 0 0 0.5",
                                Distance = "1 1"
                            }
                        }
                    });
                }
            }
            
            // Health percentage text
            container.Add(new CuiElement
            {
                Parent = UIHealthBar + "_" + entityIdString,
                Components =
                {
                    new CuiTextComponent
                    {
                        Text = $"{Math.Round(entity.health, 0)}/{Math.Round(maxHealth, 0)}",
                        FontSize = 12,
                        Align = TextAnchor.MiddleCenter,
                        Color = "1 1 1 1",
                        FadeIn = 0.1f
                    },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = "1 1"
                    }
                }
            });
            
            // Border
            container.Add(new CuiElement
            {
                Parent = UIHealthBar + "_" + entityIdString,
                Components =
                {
                    new CuiImageComponent
                    {
                        Color = config.BarColors.BorderColor,
                        Sprite = "assets/content/ui/ui.background.transparent.radial.psd"
                    },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = "1 1"
                    },
                    new CuiOutlineComponent
                    {
                        Color = config.BarColors.BorderColor,
                        Distance = "1 1"
                    }
                }
            });
            
            // Destroy any existing health bar for this entity
            CuiHelper.DestroyUi(player, UIHealthBar + "_" + entityIdString);
            CuiHelper.DestroyUi(player, UIHealthBar + "_Name_" + entityIdString);
            CuiHelper.AddUi(player, container);
            
            // Set timer to remove health bar
            timer.Once(config.HealthBarDuration, () =>
            {
                CuiHelper.DestroyUi(player, UIHealthBar + "_" + entityIdString);
                CuiHelper.DestroyUi(player, UIHealthBar + "_Name_" + entityIdString);
            });
        }
        
        private string GetHealthColor(float percentage)
        {
            if (percentage > 0.66f)
                return config.BarColors.HealthColorHigh;
            if (percentage > 0.33f)
                return config.BarColors.HealthColorMedium;
            return config.BarColors.HealthColorLow;
        }
        
        private string GetEntityName(BaseCombatEntity entity)
        {
            if (entity == null) return string.Empty;
            
            // Handle different entity types
            if (entity is BasePlayer player)
                return player.displayName;
                
            if (entity is BaseNpc npc)
            {
                // Try to get a friendly name for the NPC
                string name = npc.ShortPrefabName;
                
                // Clean up the name
                if (!string.IsNullOrEmpty(name))
                {
                    // Remove prefixes like "npc_" or "bear_"
                    if (name.Contains("_"))
                        name = name.Substring(name.LastIndexOf('_') + 1);
                        
                    // Capitalize first letter
                    if (name.Length > 0)
                        name = char.ToUpper(name[0]) + (name.Length > 1 ? name.Substring(1) : "");
                }
                
                return name;
            }
            
            // For other entities, try to get a readable name
            string entityName = entity.ShortPrefabName;
            if (string.IsNullOrEmpty(entityName))
                entityName = entity.GetType().Name;
                
            return entityName;
        }

        #endregion
    }
}