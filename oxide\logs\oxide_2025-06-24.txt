19:37 [Info] Loading Oxide Core v2.0.4128...
19:37 [Info] Loading extensions...
19:37 [Info] Loaded extension CSharp v2.0.4149 by Oxide Team and Contributors
19:37 [Info] Loaded extension MySql v2.0.3774 by Oxide Team and Contributors
19:37 [Info] Loaded extension SQLite v2.0.3804 by Oxide Team and Contributors
19:37 [Info] Unity version: 2022.3.41f1
19:37 [Info] Loaded extension Unity v2.0.3777 by Oxide Team and Contributors
19:37 [Info] Loaded extension Rust v2.0.6485 by Oxide Team and Contributors
19:37 [Info] [CSharp] Checking for updates for Oxide.Compiler.exe | Local MD5: 6256bc14d4b2ff2fb06947dde31dd058 | Last modified: 2025-06-05 00:58:26
19:37 [Info] [CSharp] Oxide.Compiler.exe is up to date
19:37 [Info] Using Covalence provider for game 'Rust'
19:37 [Info] Loading plugins...
19:37 [Info] Loaded plugin Unity v2.0.3777 by Oxide Team and Contributors
19:37 [Info] Loaded plugin Rust v2.0.6485 by Oxide Team and Contributors
19:37 [Info] Added '// Reference: Facepunch.Sqlite' in plugin 'ImageLibrary'
19:37 [Info] Added '// Reference: UnityEngine.UnityWebRequestModule' in plugin 'ImageLibrary'
19:37 [Info] [CSharp] Started Oxide.Compiler v1.0.32.0 successfully
19:37 [Info] CopyPaste, DandeeEntityHealth, DiscordMessages, ImageLibrary, Notify, PerformanceMonitor, PermissionsManager, RemoverTool, TruePVE and ZoneManager were compiled successfully in 0ms
19:37 [Info] Loaded plugin Copy Paste v4.2.1 by misticos
19:37 [Info] Loaded plugin Dandee Entity Health v1.0.0 by Dandee Studios
19:37 [Info] Loaded plugin DiscordMessages v2.1.8 by Slut
19:37 [Info] Loaded plugin Image Library v2.0.62 by Absolut & K1lly0u
19:37 [Warning] [Notify] Config update detected! Updating config values...
19:37 [Warning] [Notify] Config update completed!
19:37 [Info] Loaded plugin Notify v1.0.12 by Mevent
19:37 [Info] Loaded plugin Performance Monitor v1.2.7 by Orange
19:37 [Info] Loaded plugin PermissionsManager v2.0.9 by Steenamaroo
19:37 [Info] Loaded plugin Remover Tool v4.3.43 by Reneb/Fuji/Arainrr/Tryhard
19:37 [Info] Loaded plugin TruePVE v2.3.1 by nivex
19:37 [Info] Loaded plugin Zone Manager v3.1.8 by k1lly0u
19:38 [Info] Shutting down compiler because idle shutdown
19:38 [Info] Compiler shutdown completed
19:45 [Info] IP address from external API: ************
19:45 [Warning] Calling 'OnServerInitialized' on 'ImageLibrary v2.0.62' took 622ms
19:45 [Info] [Image Library] Starting order Notify
20:07 [Warning] Calling 'CmdPerms' on 'PermissionsManager v2.0.9' took 171ms
20:10 [Info] [Performance Monitor] Performance report was completed in 0.287 seconds.
20:10 [Warning] Calling 'cmdCompleteNow' on 'PerformanceMonitor v1.2.7' took 290ms
20:10 [Info] [Performance Monitor] Performance report was completed in 0.041 seconds.
